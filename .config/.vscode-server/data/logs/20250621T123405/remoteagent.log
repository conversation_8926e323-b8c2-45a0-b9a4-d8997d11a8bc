2025-06-21 12:34:05.084 [info] 




2025-06-21 12:34:05.094 [info] Extension host agent started.
2025-06-21 12:34:05.196 [error] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.3]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
2025-06-21 12:34:05.200 [error] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.3]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
2025-06-21 12:34:05.223 [info] Marked extension as removed github.copilot-chat-0.27.3
2025-06-21 12:34:05.232 [info] Deleted marked for removal extension from disk github.copilot-chat /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.3
2025-06-21 12:34:05.588 [info] [<unknown>][761b5836][ManagementConnection] New connection established.
2025-06-21 12:34:05.628 [info] [<unknown>][c3d6113b][ExtensionHostConnection] New connection established.
2025-06-21 12:34:05.701 [info] [<unknown>][c3d6113b][ExtensionHostConnection] <23759> Launched Extension Host Process.
2025-06-21 12:34:06.186 [info] ComputeTargetPlatform: linux-x64
2025-06-21 12:34:07.958 [info] ComputeTargetPlatform: linux-x64
2025-06-21 12:34:09.154 [info] Getting Manifest... augment.vscode-augment
2025-06-21 12:34:09.155 [info] Getting Manifest... github.copilot
2025-06-21 12:34:09.155 [info] Getting Manifest... github.copilot-chat
2025-06-21 12:34:09.155 [info] Getting Manifest... github.vscode-pull-request-github
2025-06-21 12:34:09.221 [info] Installing extension: github.copilot {"productVersion":{"version":"1.101.1","date":"2025-06-18T13:35:12.605Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"darwin-arm64"},"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"}}
2025-06-21 12:34:09.259 [info] Installing extension: augment.vscode-augment {"installPreReleaseVersion":true,"productVersion":{"version":"1.101.1","date":"2025-06-18T13:35:12.605Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"darwin-arm64"},"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"}}
2025-06-21 12:34:09.261 [info] Installing extension: github.copilot-chat {"productVersion":{"version":"1.101.1","date":"2025-06-18T13:35:12.605Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"darwin-arm64"},"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"}}
2025-06-21 12:34:09.281 [info] Installing extension: github.vscode-pull-request-github {"installPreReleaseVersion":true,"productVersion":{"version":"1.101.1","date":"2025-06-18T13:35:12.605Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"darwin-arm64"},"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"}}
2025-06-21 12:34:14.991 [info] Extension signature verification result for github.copilot-chat: Success. Internal Code: 0. Executed: true. Duration: 1754ms.
2025-06-21 12:34:14.993 [info] Extension signature verification result for github.vscode-pull-request-github: Success. Internal Code: 0. Executed: true. Duration: 1759ms.
2025-06-21 12:34:15.000 [info] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 1773ms.
2025-06-21 12:34:15.003 [info] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 1714ms.
2025-06-21 12:34:15.326 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.vscode-pull-request-github-0.113.2025062020: github.vscode-pull-request-github
2025-06-21 12:34:15.703 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.1: github.copilot-chat
2025-06-21 12:34:16.171 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.336.0: github.copilot
2025-06-21 12:34:16.357 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.487.0: augment.vscode-augment
2025-06-21 12:34:16.636 [info] Renamed to /home/<USER>/.vscode-server/extensions/github.vscode-pull-request-github-0.113.2025062020
2025-06-21 12:34:16.663 [info] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.1
2025-06-21 12:34:16.665 [info] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.487.0
2025-06-21 12:34:16.666 [info] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.336.0
2025-06-21 12:34:16.721 [info] Marked extension as removed github.vscode-pull-request-github-0.111.2025060504
2025-06-21 12:34:16.730 [info] Extension installed successfully: github.vscode-pull-request-github file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-21 12:34:16.746 [info] Marked extension as removed augment.vscode-augment-0.481.0
2025-06-21 12:34:16.756 [info] Marked extension as removed github.copilot-1.333.0
2025-06-21 12:34:16.777 [info] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-21 12:34:16.790 [info] Marked extension as removed github.copilot-chat-0.28.0
2025-06-21 12:34:16.792 [info] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-21 12:34:16.805 [info] Extension installed successfully: github.copilot-chat file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-21 12:35:46.995 [info] [<unknown>][761b5836][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
2025-06-21 12:35:47.122 [info] [<unknown>][c3d6113b][ExtensionHostConnection] <23759> Extension Host Process exited with code: 0, signal: null.
2025-06-21 12:35:47.123 [info] Cancelling previous shutdown timeout
2025-06-21 12:35:47.123 [info] Last EH closed, waiting before shutting down
2025-06-21 12:40:47.124 [info] Last EH closed, shutting down
