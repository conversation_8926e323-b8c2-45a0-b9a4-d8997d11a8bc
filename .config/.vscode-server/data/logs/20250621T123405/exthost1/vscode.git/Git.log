2025-06-21 12:34:18.587 [info] [main] Log level: Info
2025-06-21 12:34:18.587 [info] [main] Validating found git in: "git"
2025-06-21 12:34:18.587 [info] [main] Using git "2.47.2" from "git"
2025-06-21 12:34:18.587 [info] [Model][doInitialScan] Initial repository scan started
2025-06-21 12:34:18.587 [info] > git rev-parse --show-toplevel [4ms]
2025-06-21 12:34:18.587 [info] > git rev-parse --git-dir --git-common-dir [63ms]
2025-06-21 12:34:18.587 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-21 12:34:18.587 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-21 12:34:18.587 [info] > git config --get commit.template [5ms]
2025-06-21 12:34:18.587 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [117ms]
2025-06-21 12:34:18.587 [info] > git fetch [440ms]
2025-06-21 12:34:18.587 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/eddie333016/Renovision-Studio/'
2025-06-21 12:34:18.597 [info] > git config --get commit.template [3ms]
2025-06-21 12:34:18.626 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-21 12:34:18.632 [info] > git config --get commit.template [7ms]
2025-06-21 12:34:18.633 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-06-21 12:34:18.633 [info] > git config --get --local branch.main.vscode-merge-base [1ms]
2025-06-21 12:34:18.646 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [7ms]
2025-06-21 12:34:18.655 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-21 12:34:18.669 [info] > git merge-base refs/heads/main refs/remotes/origin/main [16ms]
2025-06-21 12:34:18.677 [info] > git rev-parse --show-toplevel [9ms]
2025-06-21 12:34:18.677 [info] > git merge-base refs/heads/main refs/remotes/origin/main [15ms]
2025-06-21 12:34:18.689 [info] > git diff --name-status -z --diff-filter=ADMR ad962eb108d93c2d1fd9fa0dc2b1e1540e217782...refs/remotes/origin/main [13ms]
2025-06-21 12:34:18.690 [info] > git rev-parse --show-toplevel [1ms]
2025-06-21 12:34:18.691 [info] > git diff --name-status -z --diff-filter=ADMR ad962eb108d93c2d1fd9fa0dc2b1e1540e217782...refs/remotes/origin/main [8ms]
2025-06-21 12:34:18.712 [info] > git status -z -uall [14ms]
2025-06-21 12:34:18.713 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-21 12:34:18.732 [info] > git rev-parse --show-toplevel [21ms]
2025-06-21 12:34:18.739 [info] > git rev-parse --show-toplevel [2ms]
2025-06-21 12:34:18.746 [info] > git rev-parse --show-toplevel [2ms]
2025-06-21 12:34:18.755 [info] > git rev-parse --show-toplevel [1ms]
2025-06-21 12:34:18.764 [info] > git rev-parse --show-toplevel [2ms]
2025-06-21 12:34:18.773 [info] > git rev-parse --show-toplevel [2ms]
2025-06-21 12:34:18.949 [info] > git rev-parse --show-toplevel [168ms]
2025-06-21 12:34:18.962 [info] > git rev-parse --show-toplevel [1ms]
2025-06-21 12:34:19.818 [info] > git check-ignore -v -z --stdin [150ms]
2025-06-21 12:34:19.819 [info] > git rev-parse --show-toplevel [848ms]
2025-06-21 12:34:19.839 [info] > git rev-parse --show-toplevel [13ms]
2025-06-21 12:34:20.227 [info] > git rev-parse --show-toplevel [380ms]
2025-06-21 12:34:20.249 [info] > git rev-parse --show-toplevel [11ms]
2025-06-21 12:34:20.300 [info] > git rev-parse --show-toplevel [43ms]
2025-06-21 12:34:20.699 [info] > git rev-parse --show-toplevel [391ms]
2025-06-21 12:34:20.817 [info] > git rev-parse --show-toplevel [94ms]
2025-06-21 12:34:21.334 [info] > git rev-parse --show-toplevel [66ms]
2025-06-21 12:34:21.360 [info] > git rev-parse --show-toplevel [3ms]
2025-06-21 12:34:21.362 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-21 12:34:21.387 [info] > git show --textconv :.env [18ms]
2025-06-21 12:34:21.387 [info] > git ls-files --stage -- .env [13ms]
2025-06-21 12:34:21.397 [info] > git config --get commit.template [13ms]
2025-06-21 12:34:21.405 [info] > git cat-file -s 94f666f493aa9a47391ab9b8fc27873acd84f9f4 [10ms]
2025-06-21 12:34:21.407 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-21 12:34:21.424 [info] > git status -z -uall [9ms]
2025-06-21 12:34:21.427 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-21 12:34:22.718 [info] > git config --get --local branch.main.github-pr-owner-number [192ms]
2025-06-21 12:34:22.718 [warning] [Git][config] git config failed: Failed to execute git
2025-06-21 12:34:27.055 [info] > git config --get commit.template [6ms]
2025-06-21 12:34:27.056 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-21 12:34:27.068 [info] > git status -z -uall [6ms]
2025-06-21 12:34:27.069 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-21 12:34:32.087 [info] > git config --get commit.template [6ms]
2025-06-21 12:34:32.088 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-21 12:34:32.101 [info] > git status -z -uall [7ms]
2025-06-21 12:34:32.102 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-21 12:35:43.484 [info] > git config --get commit.template [16ms]
2025-06-21 12:35:43.486 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-21 12:35:43.515 [info] > git status -z -uall [16ms]
2025-06-21 12:35:43.516 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
