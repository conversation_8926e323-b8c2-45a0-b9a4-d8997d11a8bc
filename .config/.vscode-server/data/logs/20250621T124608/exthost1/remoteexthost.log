2025-06-21 12:46:10.696 [info] Extension host with pid 9214 started
2025-06-21 12:46:10.697 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/6edecf24879131cfb79acb6b664215ba/vscode.lock': Lock acquired.
2025-06-21 12:46:10.989 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-21 12:46:10.991 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onChatParticipant:github.copilot.editsAgent'
2025-06-21 12:46:10.991 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-21 12:46:10.992 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-06-21 12:46:11.482 [error] PendingMigrationError: navigator is now a global in nodejs, please see https://aka.ms/vscode-extensions/navigator for additional info on this error.
    at get (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:1437)
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:379:47519
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:1:262
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:379:49912
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:1:262
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:379:50983
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:1:262
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:379:53169
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:1:262
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:386:2601
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:1:262
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:387:2671
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:1:262
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:387:9338
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:1:262
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:389:21442
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:1:262
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:389:25509
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:1:262
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:389:27007
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:1:262
    at Object.<anonymous> (/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/extension.js:2127:5153)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function.<anonymous> (node:internal/modules/cjs/loader:1282:12)
    at e._load (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:810)
    at t._load (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:181:22628)
    at s._load (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:173:23297)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at yY.Cb (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:212:1253)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
2025-06-21 12:46:11.832 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-21 12:46:11.833 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-21 12:46:12.005 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-06-21 12:46:12.887 [info] Eager extensions activated
2025-06-21 12:46:12.888 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-21 12:46:12.888 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-21 12:46:12.888 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-06-21 12:46:12.889 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-06-21 12:46:16.751 [info] ExtensionService#_doActivateExtension GitHub.vscode-pull-request-github, startup: false, activationEvent: 'onStartupFinished'
2025-06-21 12:46:20.005 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-21 12:46:20.005 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-21 12:46:20.005 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:json'
2025-06-21 12:46:21.355 [error] HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
    at /home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.113.2025062020/dist/extension.js:2955:29560
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at L1.execute (/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.113.2025062020/dist/extension.js:2907:21174)
2025-06-21 12:46:22.378 [error] TypeError: Converting circular structure to JSON
    --> starting at object with constructor 'TLSSocket'
    |     property 'parser' -> object with constructor 'HTTPParser'
    --- property 'socket' closes the circle
    at JSON.stringify (<anonymous>)
    at Yk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159275)
    at Function.serializeRequestArguments (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:167257)
    at $5.U (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:163497)
    at Proxy.r.<computed>.n.charCodeAt.r.<computed> (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160839)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:126:159
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:7:96
    at Array.forEach (<anonymous>)
    at q2.c (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:7:84)
    at q2.onUnexpectedError (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:7:259)
    at lt (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:7:546)
    at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/api/node/extensionHostProcess.js:361:4448
2025-06-21 12:46:23.944 [error] CodeExpectedError: cannot open vscode-userdata:/Users/<USER>/Library/Application%20Support/Code/User/keybindings.json. Detail: Unable to read file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json' (Error: Unable to resolve nonexistent file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json')
    at Pze.$tryOpenDocument (vscode-file://vscode-app/Applications/Visual%20Studio%20Code.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1283:8666)
