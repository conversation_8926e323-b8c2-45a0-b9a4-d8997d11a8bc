2025-06-21 12:46:17.467 [warning] /home/<USER>/.ssh/config: ENOENT: no such file or directory, open '/home/<USER>/.ssh/config'
2025-06-21 12:46:17.467 [info] [Activation] Extension version: 0.113.**********
2025-06-21 12:46:18.804 [info] [Authentication] Creating hub for .com
2025-06-21 12:46:19.473 [info] [Activation] Looking for git repository
2025-06-21 12:46:19.473 [info] [Activation] Found 0 repositories during activation
2025-06-21 12:46:19.473 [info] [Activation] Git repository found, initializing review manager and pr tree view.
2025-06-21 12:46:19.477 [info] [GitAPI] Registering git provider
2025-06-21 12:46:19.477 [info] [Review+0] Validate state in progress
2025-06-21 12:46:19.477 [info] [Review+0] Validating state...
2025-06-21 12:46:19.647 [info] [Activation] Repo state for file:///home/<USER>/workspace changed.
2025-06-21 12:46:19.647 [info] [Activation] Repo file:///home/<USER>/workspace has already been setup.
2025-06-21 12:46:19.669 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-21 12:46:20.198 [warning] Unable to resolve remote: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-21 12:46:20.198 [info] [FolderRepositoryManager+0] Missing upstream check failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-21 12:46:20.200 [info] [FolderRepositoryManager+0] Trying to use globalState for assignableUsers.
2025-06-21 12:46:20.209 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-21 12:46:20.216 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-21 12:46:20.218 [warning] [GitHubRepository+0] Fetching default branch failed: HttpError: Not Found - https://docs.github.com/rest/repos/repos#get-a-repository
2025-06-21 12:46:20.354 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-21 12:46:20.649 [error] [GitHubRepository+0] Error querying GraphQL API (MaxIssue): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/Renovision-Studio'.. 
2025-06-21 12:46:20.649 [error] [GitHubRepository+0] Unable to fetch issues with query: Error: GraphQL error: Could not resolve to a Repository with the name 'eddie333016/Renovision-Studio'.
2025-06-21 12:46:20.658 [info] [FolderRepositoryManager+0] Using globalState assignableUsers for 1.
2025-06-21 12:46:20.910 [error] [GitHubRepository+0] Error querying GraphQL API (GetSuggestedActors): GraphQL error: Could not resolve to a Repository with the name 'eddie333016/Renovision-Studio'.. 
2025-06-21 12:48:19.476 [error] [Review+0] Timeout occurred while validating state.
