2025-06-21 12:46:15.164 [info] Can't use the Electron fetcher in this environment.
2025-06-21 12:46:15.164 [info] Using the Node fetch fetcher.
2025-06-21 12:46:15.164 [info] [GitExtensionServiceImpl] Initializing Git extension service.
2025-06-21 12:46:15.164 [info] [GitExtensionServiceImpl] Successfully activated the vscode.git extension.
2025-06-21 12:46:15.164 [info] [GitExtensionServiceImpl] Enablement state of the vscode.git extension: true.
2025-06-21 12:46:15.164 [info] [GitExtensionServiceImpl] Successfully registered Git commit message provider.
2025-06-21 12:46:18.561 [info] Logged in as edwardbowman_nbnco
2025-06-21 12:46:20.337 [info] Got Copilot token for edwardbowman_nbnco
2025-06-21 12:46:20.345 [info] activationBlocker from 'languageModelAccess' took for 8201ms
2025-06-21 12:46:21.205 [info] Fetched model metadata in 859ms d86971fb-e544-4324-be7d-81da8b087708
2025-06-21 12:46:21.667 [info] copilot token chat_enabled: true, sku: copilot_for_business_seat_quota
2025-06-21 12:46:21.686 [info] Registering default platform agent...
2025-06-21 12:46:21.687 [info] activationBlocker from 'conversationFeature' took for 9547ms
2025-06-21 12:46:21.691 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-21 12:46:21.691 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-21 12:46:21.691 [info] Successfully registered GitHub PR title and description provider.
2025-06-21 12:46:21.691 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-21 12:46:22.322 [warning] Copilot preview features are disabled by organizational policy. Learn more: https://aka.ms/github-copilot-org-enable-features
