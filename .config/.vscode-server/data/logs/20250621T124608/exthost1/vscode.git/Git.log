2025-06-21 12:46:15.719 [info] [main] Log level: Info
2025-06-21 12:46:15.719 [info] [main] Validating found git in: "git"
2025-06-21 12:46:15.720 [info] [main] Using git "2.47.2" from "git"
2025-06-21 12:46:15.720 [info] [Model][doInitialScan] Initial repository scan started
2025-06-21 12:46:15.720 [info] > git rev-parse --show-toplevel [2ms]
2025-06-21 12:46:15.720 [info] > git rev-parse --git-dir --git-common-dir [24ms]
2025-06-21 12:46:15.720 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-21 12:46:15.720 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-21 12:46:15.720 [info] > git config --get commit.template [5ms]
2025-06-21 12:46:15.720 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [138ms]
2025-06-21 12:46:16.577 [info] > git status -z -uall [839ms]
2025-06-21 12:46:16.903 [info] > git check-ignore -v -z --stdin [286ms]
2025-06-21 12:46:16.904 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1158ms]
2025-06-21 12:46:16.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [19ms]
2025-06-21 12:46:16.969 [info] > git config --get commit.template [15ms]
2025-06-21 12:46:16.994 [info] > git config --get --local branch.main.vscode-merge-base [27ms]
2025-06-21 12:46:17.014 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [10ms]
2025-06-21 12:46:17.024 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [31ms]
2025-06-21 12:46:17.024 [info] > git rev-parse --show-toplevel [40ms]
2025-06-21 12:46:17.032 [info] > git merge-base refs/heads/main refs/remotes/origin/main [9ms]
2025-06-21 12:46:17.044 [info] > git rev-parse --show-toplevel [12ms]
2025-06-21 12:46:17.072 [info] > git diff --name-status -z --diff-filter=ADMR ad962eb108d93c2d1fd9fa0dc2b1e1540e217782...refs/remotes/origin/main [31ms]
2025-06-21 12:46:17.075 [info] > git status -z -uall [20ms]
2025-06-21 12:46:17.075 [info] > git rev-parse --show-toplevel [4ms]
2025-06-21 12:46:17.075 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-21 12:46:17.139 [info] > git rev-parse --show-toplevel [45ms]
2025-06-21 12:46:17.150 [info] > git rev-parse --show-toplevel [3ms]
2025-06-21 12:46:17.467 [info] > git rev-parse --show-toplevel [18ms]
2025-06-21 12:46:17.504 [info] > git rev-parse --show-toplevel [29ms]
2025-06-21 12:46:17.747 [info] > git rev-parse --show-toplevel [234ms]
2025-06-21 12:46:17.923 [info] > git rev-parse --show-toplevel [161ms]
2025-06-21 12:46:18.587 [info] > git rev-parse --show-toplevel [625ms]
2025-06-21 12:46:18.642 [info] > git rev-parse --show-toplevel [23ms]
2025-06-21 12:46:18.660 [info] > git rev-parse --show-toplevel [8ms]
2025-06-21 12:46:18.670 [info] > git rev-parse --show-toplevel [2ms]
2025-06-21 12:46:18.679 [info] > git rev-parse --show-toplevel [1ms]
2025-06-21 12:46:18.689 [info] > git rev-parse --show-toplevel [2ms]
2025-06-21 12:46:18.703 [info] > git rev-parse --show-toplevel [2ms]
2025-06-21 12:46:18.714 [info] > git rev-parse --show-toplevel [2ms]
2025-06-21 12:46:18.727 [info] > git rev-parse --show-toplevel [2ms]
2025-06-21 12:46:18.736 [info] > git rev-parse --show-toplevel [2ms]
2025-06-21 12:46:18.739 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-21 12:46:18.775 [info] > git show --textconv :.env [26ms]
2025-06-21 12:46:18.776 [info] > git ls-files --stage -- .env [16ms]
2025-06-21 12:46:18.807 [info] > git cat-file -s 94f666f493aa9a47391ab9b8fc27873acd84f9f4 [17ms]
2025-06-21 12:46:18.808 [info] > git config --get commit.template [34ms]
2025-06-21 12:46:18.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-21 12:46:18.972 [info] > git status -z -uall [13ms]
2025-06-21 12:46:18.973 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-21 12:46:19.415 [info] > git fetch [382ms]
2025-06-21 12:46:19.415 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/eddie333016/Renovision-Studio/'
2025-06-21 12:46:19.427 [info] > git config --get commit.template [2ms]
2025-06-21 12:46:19.445 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-21 12:46:19.643 [info] > git status -z -uall [123ms]
2025-06-21 12:46:19.644 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [107ms]
2025-06-21 12:46:19.677 [info] > git config --get commit.template [17ms]
2025-06-21 12:46:19.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [30ms]
2025-06-21 12:46:19.758 [info] > git status -z -uall [7ms]
2025-06-21 12:46:19.778 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [21ms]
2025-06-21 12:46:20.353 [info] > git config --get --local branch.main.github-pr-owner-number [144ms]
2025-06-21 12:46:20.354 [warning] [Git][config] git config failed: Failed to execute git
2025-06-21 12:46:20.558 [info] > git ls-files --stage -- .env [3ms]
2025-06-21 12:46:20.570 [info] > git cat-file -s 94f666f493aa9a47391ab9b8fc27873acd84f9f4 [4ms]
2025-06-21 12:46:20.732 [info] > git show --textconv :.env [2ms]
2025-06-21 12:46:26.058 [info] > git config --get commit.template [9ms]
2025-06-21 12:46:26.060 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-21 12:46:26.081 [info] > git status -z -uall [8ms]
2025-06-21 12:46:26.083 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-21 12:46:31.114 [info] > git config --get commit.template [15ms]
2025-06-21 12:46:31.116 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-21 12:46:31.146 [info] > git status -z -uall [14ms]
2025-06-21 12:46:31.151 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-21 12:46:36.177 [info] > git config --get commit.template [14ms]
2025-06-21 12:46:36.178 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-21 12:46:36.206 [info] > git status -z -uall [16ms]
2025-06-21 12:46:36.208 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-21 12:46:53.791 [info] > git config --get commit.template [4ms]
2025-06-21 12:46:53.804 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-21 12:46:53.826 [info] > git status -z -uall [13ms]
2025-06-21 12:46:53.828 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-21 12:46:58.871 [info] > git config --get commit.template [9ms]
2025-06-21 12:46:58.873 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-21 12:46:58.892 [info] > git status -z -uall [10ms]
2025-06-21 12:46:58.893 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-21 12:47:03.921 [info] > git config --get commit.template [10ms]
2025-06-21 12:47:03.922 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-21 12:47:03.947 [info] > git status -z -uall [12ms]
2025-06-21 12:47:03.948 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-21 12:47:08.971 [info] > git config --get commit.template [11ms]
2025-06-21 12:47:08.971 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-21 12:47:08.989 [info] > git status -z -uall [9ms]
2025-06-21 12:47:08.990 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-21 12:47:14.015 [info] > git config --get commit.template [11ms]
2025-06-21 12:47:14.018 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-21 12:47:14.045 [info] > git status -z -uall [7ms]
2025-06-21 12:47:14.049 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-21 12:47:19.071 [info] > git config --get commit.template [8ms]
2025-06-21 12:47:19.073 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-21 12:47:19.093 [info] > git status -z -uall [11ms]
2025-06-21 12:47:19.093 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-21 12:47:35.373 [info] > git config --get commit.template [9ms]
2025-06-21 12:47:35.376 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-21 12:47:35.398 [info] > git status -z -uall [12ms]
2025-06-21 12:47:35.401 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-21 12:48:00.730 [info] > git config --get commit.template [10ms]
2025-06-21 12:48:00.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-21 12:48:00.783 [info] > git status -z -uall [24ms]
2025-06-21 12:48:00.787 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-21 12:48:05.810 [info] > git config --get commit.template [12ms]
2025-06-21 12:48:05.812 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-21 12:48:05.845 [info] > git status -z -uall [17ms]
2025-06-21 12:48:05.846 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-21 12:48:10.868 [info] > git config --get commit.template [9ms]
2025-06-21 12:48:10.870 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-21 12:48:10.887 [info] > git status -z -uall [8ms]
2025-06-21 12:48:10.889 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-21 12:48:15.906 [info] > git config --get commit.template [7ms]
2025-06-21 12:48:15.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-21 12:48:15.921 [info] > git status -z -uall [8ms]
2025-06-21 12:48:15.924 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-21 12:48:20.946 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-21 12:48:20.946 [info] > git config --get commit.template [10ms]
2025-06-21 12:48:20.963 [info] > git status -z -uall [9ms]
2025-06-21 12:48:20.964 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-21 12:48:27.521 [info] > git config --get commit.template [1ms]
2025-06-21 12:48:27.539 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-21 12:48:27.573 [info] > git status -z -uall [14ms]
2025-06-21 12:48:27.575 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-21 12:48:32.599 [info] > git config --get commit.template [7ms]
2025-06-21 12:48:32.600 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-21 12:48:32.625 [info] > git status -z -uall [13ms]
2025-06-21 12:48:32.626 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-21 12:48:37.645 [info] > git config --get commit.template [8ms]
2025-06-21 12:48:37.646 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-21 12:48:37.663 [info] > git status -z -uall [8ms]
2025-06-21 12:48:37.664 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-21 12:48:42.684 [info] > git config --get commit.template [8ms]
2025-06-21 12:48:42.685 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-21 12:48:42.700 [info] > git status -z -uall [7ms]
2025-06-21 12:48:42.702 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-21 12:48:47.726 [info] > git config --get commit.template [10ms]
2025-06-21 12:48:47.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-21 12:48:47.746 [info] > git status -z -uall [11ms]
2025-06-21 12:48:47.747 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-21 12:48:52.761 [info] > git config --get commit.template [2ms]
2025-06-21 12:48:52.771 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-21 12:48:52.787 [info] > git status -z -uall [7ms]
2025-06-21 12:48:52.789 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-21 12:49:10.785 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-21 12:49:10.786 [info] > git config --get commit.template [9ms]
2025-06-21 12:49:10.804 [info] > git status -z -uall [9ms]
2025-06-21 12:49:10.806 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-21 12:49:15.826 [info] > git config --get commit.template [8ms]
2025-06-21 12:49:15.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-21 12:49:15.848 [info] > git status -z -uall [12ms]
2025-06-21 12:49:15.849 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-21 12:49:20.871 [info] > git config --get commit.template [9ms]
2025-06-21 12:49:20.872 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-21 12:49:20.891 [info] > git status -z -uall [9ms]
2025-06-21 12:49:20.893 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-21 12:49:25.914 [info] > git config --get commit.template [10ms]
2025-06-21 12:49:25.917 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-21 12:49:25.935 [info] > git status -z -uall [9ms]
2025-06-21 12:49:25.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-21 12:49:32.959 [info] > git config --get commit.template [10ms]
2025-06-21 12:49:32.961 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-21 12:49:32.988 [info] > git status -z -uall [13ms]
2025-06-21 12:49:32.991 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
