*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[12:46:08] 




[12:46:08] Extension host agent started.
[12:46:08] Deleted marked for removal extension from disk github.copilot-chat /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.0
[12:46:08] Deleted marked for removal extension from disk augment.vscode-augment /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.481.0
[12:46:08] Deleted marked for removal extension from disk github.vscode-pull-request-github /home/<USER>/.vscode-server/extensions/github.vscode-pull-request-github-0.111.**********
[12:46:08] Deleted marked for removal extension from disk github.copilot /home/<USER>/.vscode-server/extensions/github.copilot-1.333.0
[12:46:08] [<unknown>][d6b5216b][ManagementConnection] New connection established.
[12:46:08] [<unknown>][fd735797][ExtensionHostConnection] New connection established.
[12:46:09] [<unknown>][fd735797][ExtensionHostConnection] <9214> Launched Extension Host Process.
[12:46:09] ComputeTargetPlatform: linux-x64
[12:46:10] ComputeTargetPlatform: linux-x64
